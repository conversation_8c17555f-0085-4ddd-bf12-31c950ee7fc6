// Main entry point for Jardinains game

let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('gameCanvas');
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const restartBtn = document.getElementById('restartBtn');
    const startGameBtn = document.getElementById('startGameBtn');
    const playAgainBtn = document.getElementById('playAgainBtn');
    const nextLevelBtn = document.getElementById('nextLevelBtn');
    
    // Initialize game
    game = new Game(canvas);
    
    // Button event listeners
    startBtn.addEventListener('click', function() {
        if (game.gameState === 'start' || game.gameState === 'gameOver') {
            hideAllScreens();
            game.start();
            startBtn.disabled = true;
            pauseBtn.disabled = false;
        }
    });
    
    pauseBtn.addEventListener('click', function() {
        game.pause();
        if (game.gameState === 'paused') {
            pauseBtn.textContent = 'Resume';
        } else {
            pauseBtn.textContent = 'Pause';
        }
    });
    
    restartBtn.addEventListener('click', function() {
        hideAllScreens();
        game.restart();
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Pause';
    });
    
    startGameBtn.addEventListener('click', function() {
        hideAllScreens();
        game.start();
        startBtn.disabled = true;
        pauseBtn.disabled = false;
    });
    
    playAgainBtn.addEventListener('click', function() {
        hideAllScreens();
        game.restart();
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Pause';
    });
    
    nextLevelBtn.addEventListener('click', function() {
        game.nextLevel();
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key) {
            case 'p':
            case 'P':
                if (game.gameState === 'playing' || game.gameState === 'paused') {
                    pauseBtn.click();
                }
                break;
            case 'r':
            case 'R':
                if (e.ctrlKey) {
                    e.preventDefault();
                    restartBtn.click();
                }
                break;
            case 'Enter':
                if (game.gameState === 'start') {
                    startBtn.click();
                } else if (game.gameState === 'gameOver') {
                    playAgainBtn.click();
                } else if (game.gameState === 'levelComplete') {
                    nextLevelBtn.click();
                }
                break;
            case 'Escape':
                if (game.gameState === 'playing') {
                    pauseBtn.click();
                }
                break;
            case 'F3':
                e.preventDefault();
                game.toggleDebugInfo();
                break;
        }
    });
    
    // Canvas click to start/resume
    canvas.addEventListener('click', function() {
        if (game.gameState === 'start') {
            startBtn.click();
        } else if (game.gameState === 'paused') {
            pauseBtn.click();
        }
    });
    
    // Touch controls for mobile
    let touchStartX = 0;
    let touchStartY = 0;
    
    canvas.addEventListener('touchstart', function(e) {
        e.preventDefault();
        const touch = e.touches[0];
        touchStartX = touch.clientX;
        touchStartY = touch.clientY;
    });
    
    canvas.addEventListener('touchmove', function(e) {
        e.preventDefault();
        if (game.gameState !== 'playing') return;
        
        const touch = e.touches[0];
        const rect = canvas.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        
        // Move paddle to touch position
        const paddleX = (x / rect.width) * canvas.width - game.paddle.width / 2;
        game.paddle.position.x = Utils.clamp(paddleX, 0, canvas.width - game.paddle.width);
    });
    
    canvas.addEventListener('touchend', function(e) {
        e.preventDefault();
        const touch = e.changedTouches[0];
        const deltaY = touch.clientY - touchStartY;
        
        // Swipe up to release sticky balls
        if (deltaY < -50 && game.paddle.isSticky) {
            game.paddle.releaseStickyBalls();
        }
    });
    
    // Prevent context menu on canvas
    canvas.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        resizeCanvas();
    });
    
    // Initial canvas resize
    resizeCanvas();
    
    // Show instructions initially
    showInstructions();
});

function hideAllScreens() {
    document.getElementById('startScreen').classList.add('hidden');
    document.getElementById('gameOverScreen').classList.add('hidden');
    document.getElementById('levelCompleteScreen').classList.add('hidden');
}

function resizeCanvas() {
    const canvas = document.getElementById('gameCanvas');
    const container = document.getElementById('gameContainer');
    
    // Get container dimensions
    const containerRect = container.getBoundingClientRect();
    const maxWidth = Math.min(800, window.innerWidth - 40);
    const maxHeight = Math.min(600, window.innerHeight - 200);
    
    // Calculate aspect ratio
    const aspectRatio = 800 / 600;
    let newWidth = maxWidth;
    let newHeight = newWidth / aspectRatio;
    
    if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = newHeight * aspectRatio;
    }
    
    // Set canvas display size
    canvas.style.width = newWidth + 'px';
    canvas.style.height = newHeight + 'px';
    
    // Keep internal resolution the same for consistent gameplay
    canvas.width = 800;
    canvas.height = 600;
}

function showInstructions() {
    // Add some interactive hints
    const instructions = document.getElementById('instructions');
    
    // Add keyboard shortcut information
    const shortcutsDiv = document.createElement('div');
    shortcutsDiv.innerHTML = `
        <h4 style="color: #ffeb3b; margin-top: 15px;">Keyboard Shortcuts:</h4>
        <ul style="list-style: none; padding-left: 0;">
            <li>🎮 <strong>P</strong> - Pause/Resume</li>
            <li>🔄 <strong>Ctrl+R</strong> - Restart Game</li>
            <li>🚀 <strong>Enter</strong> - Start/Continue</li>
            <li>⏸️ <strong>Escape</strong> - Pause</li>
            <li>🎯 <strong>Space</strong> - Release Sticky Balls</li>
        </ul>
    `;
    instructions.appendChild(shortcutsDiv);
}

// Game statistics tracking
class GameStats {
    constructor() {
        this.gamesPlayed = 0;
        this.highScore = 0;
        this.totalScore = 0;
        this.bricksDestroyed = 0;
        this.gnomesBounced = 0;
        this.powerUpsCollected = 0;
        this.levelsCompleted = 0;
        
        this.loadStats();
    }
    
    updateStats(game) {
        this.gamesPlayed++;
        this.totalScore += game.score;
        this.levelsCompleted = Math.max(this.levelsCompleted, game.level - 1);
        
        if (game.score > this.highScore) {
            this.highScore = game.score;
            this.showNewHighScore();
        }
        
        this.saveStats();
    }
    
    saveStats() {
        const stats = {
            gamesPlayed: this.gamesPlayed,
            highScore: this.highScore,
            totalScore: this.totalScore,
            bricksDestroyed: this.bricksDestroyed,
            gnomesBounced: this.gnomesBounced,
            powerUpsCollected: this.powerUpsCollected,
            levelsCompleted: this.levelsCompleted
        };
        
        localStorage.setItem('jardinains_stats', JSON.stringify(stats));
    }
    
    loadStats() {
        const saved = localStorage.getItem('jardinains_stats');
        if (saved) {
            const stats = JSON.parse(saved);
            Object.assign(this, stats);
        }
    }
    
    showNewHighScore() {
        // Flash the score display
        const scoreElement = document.getElementById('score');
        scoreElement.classList.add('flash');
        setTimeout(() => {
            scoreElement.classList.remove('flash');
        }, 1000);
        
        console.log('New High Score!', this.highScore);
    }
    
    getStats() {
        return {
            gamesPlayed: this.gamesPlayed,
            highScore: this.highScore,
            averageScore: this.gamesPlayed > 0 ? Math.round(this.totalScore / this.gamesPlayed) : 0,
            bricksDestroyed: this.bricksDestroyed,
            gnomesBounced: this.gnomesBounced,
            powerUpsCollected: this.powerUpsCollected,
            levelsCompleted: this.levelsCompleted
        };
    }
}

// Initialize game statistics
const gameStats = new GameStats();

// Add stats display to game over screen
document.addEventListener('DOMContentLoaded', function() {
    const gameOverScreen = document.getElementById('gameOverScreen');
    const statsDiv = document.createElement('div');
    statsDiv.innerHTML = `
        <div style="margin-top: 20px; font-size: 14px;">
            <h3>Your Stats</h3>
            <p>High Score: <span id="statHighScore">${gameStats.highScore}</span></p>
            <p>Games Played: <span id="statGamesPlayed">${gameStats.gamesPlayed}</span></p>
            <p>Levels Completed: <span id="statLevelsCompleted">${gameStats.levelsCompleted}</span></p>
        </div>
    `;
    gameOverScreen.appendChild(statsDiv);
});

// Update stats when game ends
const originalGameOver = Game.prototype.gameOver;
Game.prototype.gameOver = function() {
    gameStats.updateStats(this);
    
    // Update stats display
    document.getElementById('statHighScore').textContent = gameStats.highScore;
    document.getElementById('statGamesPlayed').textContent = gameStats.gamesPlayed;
    document.getElementById('statLevelsCompleted').textContent = gameStats.levelsCompleted;
    
    originalGameOver.call(this);
};

// Console commands for debugging
window.jardinains = {
    game: () => game,
    stats: () => gameStats.getStats(),
    cheat: {
        addLife: () => { game.lives++; game.updateUI(); },
        nextLevel: () => game.levelComplete(),
        addScore: (points) => { game.score += points; game.updateUI(); },
        clearBricks: () => { game.bricks.forEach(brick => brick.active = false); },
        addBalls: (count = 3) => {
            for (let i = 0; i < count; i++) {
                const ball = new Ball(game.width / 2 + i * 20, game.height / 2);
                ball.velocity = new Vector2D(Utils.random(-200, 200), -300);
                game.balls.push(ball);
            }
        }
    }
};

console.log('🧙‍♂️ Welcome to Jardinains! 🧙‍♂️');
console.log('Type "jardinains.cheat" in console for debug commands');
console.log('Type "jardinains.stats()" to see your statistics');
