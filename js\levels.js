// Level system for Jardinains

class LevelManager {
    constructor() {
        this.currentLevel = 1;
        this.levels = this.generateLevels();
    }
    
    generateLevels() {
        const levels = [];
        
        // Level 1 - Simple introduction
        levels.push({
            number: 1,
            name: "Garden Entrance",
            bricks: this.createSimplePattern(),
            gnomeChance: 0.2,
            powerUpChance: 0.1
        });
        
        // Level 2 - More bricks
        levels.push({
            number: 2,
            name: "Flower Bed",
            bricks: this.createFlowerPattern(),
            gnomeChance: 0.3,
            powerUpChance: 0.15
        });
        
        // Level 3 - Stronger bricks
        levels.push({
            number: 3,
            name: "Gnome Village",
            bricks: this.createVillagePattern(),
            gnomeChance: 0.4,
            powerUpChance: 0.15
        });
        
        // Level 4 - Complex pattern
        levels.push({
            number: 4,
            name: "Mushroom Forest",
            bricks: this.createMushroomPattern(),
            gnomeChance: 0.4,
            powerUpChance: 0.2
        });
        
        // Level 5 - Boss level
        levels.push({
            number: 5,
            name: "Garden Castle",
            bricks: this.createCastlePattern(),
            gnomeChance: 0.5,
            powerUpChance: 0.2
        });
        
        // Generate additional levels procedurally
        for (let i = 6; i <= 20; i++) {
            levels.push({
                number: i,
                name: `Level ${i}`,
                bricks: this.createRandomPattern(i),
                gnomeChance: Math.min(0.6, 0.2 + (i * 0.05)),
                powerUpChance: Math.min(0.25, 0.1 + (i * 0.01))
            });
        }
        
        return levels;
    }
    
    createSimplePattern() {
        const bricks = [];
        const rows = 4;
        const cols = 10;
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = startX + col * (brickWidth + spacing);
                const y = startY + row * (brickHeight + spacing);
                const type = row === 0 ? 'strong' : 'normal';
                bricks.push({ x, y, type });
            }
        }
        
        return bricks;
    }
    
    createFlowerPattern() {
        const bricks = [];
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        // Create flower shape
        const pattern = [
            [0,0,1,1,1,1,1,0,0],
            [0,1,2,2,2,2,2,1,0],
            [1,2,2,3,3,3,2,2,1],
            [1,2,3,3,3,3,3,2,1],
            [1,2,2,3,3,3,2,2,1],
            [0,1,2,2,2,2,2,1,0],
            [0,0,1,1,1,1,1,0,0]
        ];
        
        const types = ['', 'normal', 'strong', 'super'];
        
        for (let row = 0; row < pattern.length; row++) {
            for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col] > 0) {
                    const x = startX + col * (brickWidth + spacing);
                    const y = startY + row * (brickHeight + spacing);
                    const type = types[pattern[row][col]];
                    bricks.push({ x, y, type });
                }
            }
        }
        
        return bricks;
    }
    
    createVillagePattern() {
        const bricks = [];
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        // Create village houses pattern
        const pattern = [
            [2,2,0,2,2,0,2,2,0,2,2],
            [1,1,1,1,1,1,1,1,1,1,1],
            [1,3,1,1,3,1,1,3,1,1,3],
            [1,1,1,1,1,1,1,1,1,1,1],
            [2,2,2,2,2,2,2,2,2,2,2],
            [1,1,1,1,1,1,1,1,1,1,1]
        ];
        
        const types = ['', 'normal', 'strong', 'super'];
        
        for (let row = 0; row < pattern.length; row++) {
            for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col] > 0) {
                    const x = startX + col * (brickWidth + spacing);
                    const y = startY + row * (brickHeight + spacing);
                    const type = types[pattern[row][col]];
                    bricks.push({ x, y, type });
                }
            }
        }
        
        return bricks;
    }
    
    createMushroomPattern() {
        const bricks = [];
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        // Create mushroom shapes
        const pattern = [
            [0,0,2,2,2,0,0,2,2,2,0,0],
            [0,2,1,1,1,2,2,1,1,1,2,0],
            [2,1,1,1,1,1,1,1,1,1,1,2],
            [0,0,3,0,0,0,0,0,3,0,0,0],
            [0,0,3,0,0,0,0,0,3,0,0,0],
            [1,1,1,1,1,1,1,1,1,1,1,1]
        ];
        
        const types = ['', 'normal', 'strong', 'super'];
        
        for (let row = 0; row < pattern.length; row++) {
            for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col] > 0) {
                    const x = startX + col * (brickWidth + spacing);
                    const y = startY + row * (brickHeight + spacing);
                    const type = types[pattern[row][col]];
                    bricks.push({ x, y, type });
                }
            }
        }
        
        return bricks;
    }
    
    createCastlePattern() {
        const bricks = [];
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        // Create castle pattern
        const pattern = [
            [3,0,3,0,3,3,3,0,3,0,3],
            [3,3,3,3,3,3,3,3,3,3,3],
            [2,2,2,2,2,2,2,2,2,2,2],
            [2,1,2,1,2,2,2,1,2,1,2],
            [2,2,2,2,2,2,2,2,2,2,2],
            [1,1,1,1,1,4,1,1,1,1,1],
            [1,1,1,1,1,4,1,1,1,1,1]
        ];
        
        const types = ['', 'normal', 'strong', 'super', 'metal'];
        
        for (let row = 0; row < pattern.length; row++) {
            for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col] > 0) {
                    const x = startX + col * (brickWidth + spacing);
                    const y = startY + row * (brickHeight + spacing);
                    const type = types[pattern[row][col]];
                    bricks.push({ x, y, type });
                }
            }
        }
        
        return bricks;
    }
    
    createRandomPattern(levelNumber) {
        const bricks = [];
        const brickWidth = 60;
        const brickHeight = 20;
        const spacing = 5;
        const startX = 50;
        const startY = 50;
        
        const rows = Math.min(8, 4 + Math.floor(levelNumber / 3));
        const cols = 11;
        const density = Math.min(0.8, 0.5 + (levelNumber * 0.02));
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                if (Math.random() < density) {
                    const x = startX + col * (brickWidth + spacing);
                    const y = startY + row * (brickHeight + spacing);
                    
                    // Determine brick type based on level
                    let type = 'normal';
                    const rand = Math.random();
                    
                    if (levelNumber >= 10 && rand < 0.1) {
                        type = 'metal';
                    } else if (levelNumber >= 5 && rand < 0.2) {
                        type = 'super';
                    } else if (levelNumber >= 3 && rand < 0.3) {
                        type = 'strong';
                    }
                    
                    bricks.push({ x, y, type });
                }
            }
        }
        
        return bricks;
    }
    
    getCurrentLevel() {
        return this.levels[this.currentLevel - 1] || this.levels[this.levels.length - 1];
    }
    
    nextLevel() {
        this.currentLevel++;
        return this.getCurrentLevel();
    }
    
    resetToLevel(levelNumber) {
        this.currentLevel = levelNumber;
        return this.getCurrentLevel();
    }
    
    createBricksForLevel(level) {
        const bricks = [];
        
        level.bricks.forEach(brickData => {
            const brick = new Brick(brickData.x, brickData.y, brickData.type);
            
            // Override gnome and power-up chances based on level
            brick.hasGnome = Math.random() < level.gnomeChance;
            if (!brick.powerUpType && Math.random() < level.powerUpChance) {
                brick.powerUpType = brick.determinePowerUp();
            }
            
            bricks.push(brick);
        });
        
        return bricks;
    }
}
