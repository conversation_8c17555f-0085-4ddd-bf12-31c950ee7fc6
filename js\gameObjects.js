// Game objects for Jardinains

// Base GameObject class
class GameObject {
    constructor(x, y, width, height) {
        this.position = new Vector2D(x, y);
        this.width = width;
        this.height = height;
        this.active = true;
    }
    
    get bounds() {
        return new Rectangle(this.position.x, this.position.y, this.width, this.height);
    }
    
    update(deltaTime) {
        // Override in subclasses
    }
    
    draw(ctx) {
        // Override in subclasses
    }
}

// Paddle class
class Paddle extends GameObject {
    constructor(x, y) {
        super(x, y, 100, 15);
        this.speed = 400;
        this.velocity = new Vector2D(0, 0);
        this.color = '#8b4513';
        this.stickyBalls = [];
        this.isSticky = false;
        this.stickyTimer = 0;
    }
    
    update(deltaTime, canvasWidth) {
        // Update position
        this.position = this.position.add(this.velocity.multiply(deltaTime));
        
        // Keep paddle within canvas bounds
        this.position.x = Utils.clamp(this.position.x, 0, canvasWidth - this.width);
        
        // Update sticky timer
        if (this.isSticky) {
            this.stickyTimer -= deltaTime;
            if (this.stickyTimer <= 0) {
                this.isSticky = false;
                this.releaseStickyBalls();
            }
        }
    }
    
    draw(ctx) {
        // Draw paddle
        ctx.fillStyle = this.color;
        ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
        
        // Draw sticky effect
        if (this.isSticky) {
            ctx.strokeStyle = '#9c27b0';
            ctx.lineWidth = 3;
            ctx.strokeRect(this.position.x - 2, this.position.y - 2, this.width + 4, this.height + 4);
        }
        
        // Draw gnome on paddle
        this.drawGnome(ctx);
    }
    
    drawGnome(ctx) {
        const centerX = this.position.x + this.width / 2;
        const centerY = this.position.y;
        
        // Gnome hat
        ctx.fillStyle = '#ff0000';
        ctx.beginPath();
        ctx.moveTo(centerX - 8, centerY);
        ctx.lineTo(centerX + 8, centerY);
        ctx.lineTo(centerX, centerY - 15);
        ctx.closePath();
        ctx.fill();
        
        // Gnome head
        ctx.fillStyle = '#ffdbac';
        ctx.beginPath();
        ctx.arc(centerX, centerY - 5, 6, 0, Math.PI * 2);
        ctx.fill();
        
        // Gnome beard
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(centerX - 4, centerY - 2, 8, 6);
    }
    
    moveLeft() {
        this.velocity.x = -this.speed;
    }
    
    moveRight() {
        this.velocity.x = this.speed;
    }
    
    stop() {
        this.velocity.x = 0;
    }
    
    resize(newWidth) {
        this.width = newWidth;
    }
    
    makeSticky(duration = 10) {
        this.isSticky = true;
        this.stickyTimer = duration;
    }
    
    attachBall(ball) {
        if (this.isSticky) {
            this.stickyBalls.push(ball);
            ball.isStuck = true;
            ball.stuckOffset = ball.position.x - this.position.x;
        }
    }
    
    releaseStickyBalls() {
        this.stickyBalls.forEach(ball => {
            ball.isStuck = false;
            ball.velocity.y = -Math.abs(ball.velocity.y);
        });
        this.stickyBalls = [];
    }
}

// Ball class
class Ball extends GameObject {
    constructor(x, y) {
        super(x, y, 12, 12);
        this.velocity = new Vector2D(0, -300);
        this.speed = 300;
        this.color = '#ffffff';
        this.trail = [];
        this.isStuck = false;
        this.stuckOffset = 0;
    }
    
    update(deltaTime, canvasWidth, canvasHeight, paddle) {
        if (this.isStuck && paddle) {
            // Follow paddle when stuck
            this.position.x = paddle.position.x + this.stuckOffset;
            this.position.y = paddle.position.y - this.height;
            return;
        }
        
        // Add current position to trail
        this.trail.push({x: this.position.x, y: this.position.y, life: 0.3});
        if (this.trail.length > 10) this.trail.shift();
        
        // Update trail
        this.trail = this.trail.filter(point => {
            point.life -= deltaTime;
            return point.life > 0;
        });
        
        // Update position
        this.position = this.position.add(this.velocity.multiply(deltaTime));
        
        // Bounce off walls
        if (this.position.x <= 0 || this.position.x >= canvasWidth - this.width) {
            this.velocity.x = -this.velocity.x;
            this.position.x = Utils.clamp(this.position.x, 0, canvasWidth - this.width);
            SoundManager.playBounce();
        }
        
        if (this.position.y <= 0) {
            this.velocity.y = -this.velocity.y;
            this.position.y = 0;
            SoundManager.playBounce();
        }
        
        // Check if ball is below paddle (lost)
        if (this.position.y > canvasHeight) {
            this.active = false;
        }
    }
    
    draw(ctx) {
        // Draw trail
        this.trail.forEach((point, index) => {
            const alpha = point.life / 0.3;
            ctx.save();
            ctx.globalAlpha = alpha * 0.5;
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(point.x + this.width/2, point.y + this.height/2, 
                   (this.width/2) * (index / this.trail.length), 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
        
        // Draw ball
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.position.x + this.width/2, this.position.y + this.height/2, 
               this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw ball highlight
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.beginPath();
        ctx.arc(this.position.x + this.width/2 - 2, this.position.y + this.height/2 - 2, 
               this.width/4, 0, Math.PI * 2);
        ctx.fill();
    }
    
    bounceOffPaddle(paddle) {
        const ballCenter = this.position.x + this.width / 2;
        const paddleCenter = paddle.position.x + paddle.width / 2;
        const hitPos = (ballCenter - paddleCenter) / (paddle.width / 2);
        
        // Calculate new velocity based on hit position
        const angle = hitPos * Math.PI / 3; // Max 60 degrees
        this.velocity.x = Math.sin(angle) * this.speed;
        this.velocity.y = -Math.abs(Math.cos(angle) * this.speed);
        
        SoundManager.playBounce();
        
        // Check for sticky paddle
        if (paddle.isSticky) {
            paddle.attachBall(this);
        }
    }
    
    setSpeed(newSpeed) {
        const direction = this.velocity.normalize();
        this.speed = newSpeed;
        this.velocity = direction.multiply(this.speed);
    }
}

// Brick class
class Brick extends GameObject {
    constructor(x, y, type = 'normal') {
        super(x, y, 60, 20);
        this.type = type;
        this.hitPoints = this.getHitPointsForType(type);
        this.maxHitPoints = this.hitPoints;
        this.color = this.getColorForType(type);
        this.hasGnome = Utils.random(0, 1) < 0.3; // 30% chance of gnome
        this.powerUpType = this.determinePowerUp();
    }
    
    getHitPointsForType(type) {
        const hitPoints = {
            'normal': 1,
            'strong': 2,
            'super': 3,
            'metal': 5,
            'explosive': 1
        };
        return hitPoints[type] || 1;
    }
    
    getColorForType(type) {
        const colors = {
            'normal': Utils.randomColor(Colors.BRICK_COLORS),
            'strong': '#ff6b6b',
            'super': '#4ecdc4',
            'metal': '#95a5a6',
            'explosive': '#e74c3c'
        };
        return colors[type] || '#96ceb4';
    }
    
    determinePowerUp() {
        if (Utils.random(0, 1) < 0.15) { // 15% chance
            const powerUps = ['MULTI_BALL', 'LARGE_PADDLE', 'SMALL_PADDLE', 
                            'FAST_BALL', 'SLOW_BALL', 'EXTRA_LIFE', 'STICKY_PADDLE'];
            return powerUps[Utils.randomInt(0, powerUps.length - 1)];
        }
        return null;
    }
    
    hit() {
        this.hitPoints--;
        if (this.hitPoints <= 0) {
            this.active = false;
            return true; // Brick destroyed
        }
        return false; // Brick damaged but not destroyed
    }
    
    draw(ctx) {
        // Draw brick with damage indication
        const damageRatio = this.hitPoints / this.maxHitPoints;
        
        ctx.fillStyle = this.color;
        ctx.globalAlpha = 0.3 + (damageRatio * 0.7);
        ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
        ctx.globalAlpha = 1;
        
        // Draw border
        ctx.strokeStyle = '#2c3e50';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.position.x, this.position.y, this.width, this.height);
        
        // Draw gnome indicator
        if (this.hasGnome) {
            ctx.fillStyle = '#8b4513';
            ctx.beginPath();
            ctx.arc(this.position.x + this.width - 8, this.position.y + 8, 4, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // Draw power-up indicator
        if (this.powerUpType) {
            ctx.fillStyle = Colors.POWERUP_COLORS[this.powerUpType];
            ctx.fillRect(this.position.x + 2, this.position.y + 2, 8, 8);
        }
        
        // Draw hit points for strong bricks
        if (this.maxHitPoints > 1) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.hitPoints.toString(), 
                        this.position.x + this.width/2, 
                        this.position.y + this.height/2 + 4);
        }
    }
}

// Garden Gnome (Nain) class - the unique feature of Jardinains!
class Gnome extends GameObject {
    constructor(x, y) {
        super(x, y, 16, 20);
        this.velocity = new Vector2D(Utils.random(-50, 50), Utils.random(20, 60));
        this.color = Utils.randomColor(Colors.GNOME_COLORS);
        this.throwTimer = 0;
        this.throwInterval = Utils.random(2, 4); // Throw every 2-4 seconds
        this.bounceCount = 0;
        this.maxBounces = Utils.randomInt(3, 8);
        this.isBeingBounced = false;
        this.bounceVelocity = new Vector2D(0, 0);
    }

    update(deltaTime, canvasWidth, canvasHeight) {
        if (this.isBeingBounced) {
            // Gnome is being bounced by ball
            this.position = this.position.add(this.bounceVelocity.multiply(deltaTime));
            this.bounceVelocity.y += 300 * deltaTime; // gravity

            // Bounce off walls
            if (this.position.x <= 0 || this.position.x >= canvasWidth - this.width) {
                this.bounceVelocity.x = -this.bounceVelocity.x;
                this.position.x = Utils.clamp(this.position.x, 0, canvasWidth - this.width);
            }

            // Check if gnome hits ground
            if (this.position.y >= canvasHeight - this.height) {
                this.active = false;
            }
        } else {
            // Normal gnome movement
            this.position = this.position.add(this.velocity.multiply(deltaTime));

            // Bounce off walls
            if (this.position.x <= 0 || this.position.x >= canvasWidth - this.width) {
                this.velocity.x = -this.velocity.x;
                this.position.x = Utils.clamp(this.position.x, 0, canvasWidth - this.width);
            }

            // Fall down slowly
            this.velocity.y += 50 * deltaTime;

            // Remove if falls off screen
            if (this.position.y > canvasHeight) {
                this.active = false;
            }

            // Throwing logic
            this.throwTimer += deltaTime;
        }
    }

    draw(ctx) {
        ctx.save();

        // Gnome body
        ctx.fillStyle = this.color;
        ctx.fillRect(this.position.x + 4, this.position.y + 8, 8, 12);

        // Gnome hat
        ctx.fillStyle = '#ff0000';
        ctx.beginPath();
        ctx.moveTo(this.position.x + 2, this.position.y + 8);
        ctx.lineTo(this.position.x + 14, this.position.y + 8);
        ctx.lineTo(this.position.x + 8, this.position.y);
        ctx.closePath();
        ctx.fill();

        // Gnome head
        ctx.fillStyle = '#ffdbac';
        ctx.beginPath();
        ctx.arc(this.position.x + 8, this.position.y + 10, 4, 0, Math.PI * 2);
        ctx.fill();

        // Gnome beard
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.position.x + 6, this.position.y + 12, 4, 4);

        // Eyes
        ctx.fillStyle = '#000000';
        ctx.fillRect(this.position.x + 6, this.position.y + 9, 1, 1);
        ctx.fillRect(this.position.x + 9, this.position.y + 9, 1, 1);

        // Arms (if throwing)
        if (this.throwTimer > this.throwInterval - 0.5) {
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(this.position.x + 4, this.position.y + 12);
            ctx.lineTo(this.position.x, this.position.y + 8);
            ctx.stroke();
        }

        ctx.restore();
    }

    shouldThrow() {
        if (this.throwTimer >= this.throwInterval && !this.isBeingBounced) {
            this.throwTimer = 0;
            this.throwInterval = Utils.random(2, 4);
            return true;
        }
        return false;
    }

    startBouncing(velocity) {
        this.isBeingBounced = true;
        this.bounceVelocity = velocity;
        this.bounceCount++;
    }

    canBeBounced() {
        return this.bounceCount < this.maxBounces;
    }
}

// Projectile class for gnome thrown objects
class Projectile extends GameObject {
    constructor(x, y, targetX, targetY) {
        super(x, y, 6, 6);

        // Calculate velocity to reach target
        const distance = Utils.distance(x, y, targetX, targetY);
        const time = distance / 150; // Projectile speed
        this.velocity = new Vector2D(
            (targetX - x) / time,
            (targetY - y) / time
        );

        this.type = this.getRandomProjectileType();
        this.color = this.getColorForType();
        this.rotation = 0;
        this.rotationSpeed = Utils.random(5, 15);
    }

    getRandomProjectileType() {
        const types = ['pot', 'rock', 'flower', 'mushroom'];
        return types[Utils.randomInt(0, types.length - 1)];
    }

    getColorForType() {
        const colors = {
            'pot': '#8b4513',
            'rock': '#7f8c8d',
            'flower': '#e74c3c',
            'mushroom': '#f39c12'
        };
        return colors[this.type] || '#95a5a6';
    }

    update(deltaTime, canvasWidth, canvasHeight) {
        this.position = this.position.add(this.velocity.multiply(deltaTime));
        this.rotation += this.rotationSpeed * deltaTime;

        // Remove if off screen
        if (this.position.x < -this.width || this.position.x > canvasWidth ||
            this.position.y < -this.height || this.position.y > canvasHeight) {
            this.active = false;
        }
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.position.x + this.width/2, this.position.y + this.height/2);
        ctx.rotate(this.rotation);

        ctx.fillStyle = this.color;

        switch(this.type) {
            case 'pot':
                ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
                ctx.strokeStyle = '#654321';
                ctx.lineWidth = 1;
                ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
                break;
            case 'rock':
                ctx.beginPath();
                ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
                ctx.fill();
                break;
            case 'flower':
                // Flower petals
                for (let i = 0; i < 5; i++) {
                    ctx.save();
                    ctx.rotate((i * Math.PI * 2) / 5);
                    ctx.beginPath();
                    ctx.ellipse(0, -this.width/3, this.width/4, this.width/6, 0, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
                // Center
                ctx.fillStyle = '#f1c40f';
                ctx.beginPath();
                ctx.arc(0, 0, this.width/6, 0, Math.PI * 2);
                ctx.fill();
                break;
            case 'mushroom':
                // Mushroom cap
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(0, -this.height/4, this.width/2, 0, Math.PI);
                ctx.fill();
                // Mushroom stem
                ctx.fillStyle = '#ecf0f1';
                ctx.fillRect(-this.width/6, -this.height/4, this.width/3, this.height/2);
                break;
        }

        ctx.restore();
    }
}
