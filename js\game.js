// Main game class for Jardinains

class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        
        // Game state
        this.gameState = 'start'; // 'start', 'playing', 'paused', 'gameOver', 'levelComplete'
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // Game objects
        this.paddle = new Paddle(this.width / 2 - 50, this.height - 50);
        this.balls = [new Ball(this.width / 2, this.height - 70)];
        this.bricks = [];
        this.gnomes = [];
        this.projectiles = [];
        this.powerUps = [];
        
        // Managers
        this.levelManager = new LevelManager();
        this.powerUpManager = new PowerUpManager(this);
        this.particleSystem = new ParticleSystem();
        
        // Input handling
        this.keys = {};
        this.setupInput();
        
        // Game timing
        this.lastTime = 0;
        this.gameLoop = this.gameLoop.bind(this);

        // Performance monitoring
        this.fps = 0;
        this.frameCount = 0;
        this.fpsTimer = 0;
        this.showDebugInfo = false;
        
        // Initialize first level
        this.loadLevel();
        
        // Sound initialization
        SoundManager.init();
    }
    
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.key.toLowerCase()] = true;
            
            // Special key handling
            if (e.key === ' ' && this.gameState === 'playing') {
                e.preventDefault();
                this.paddle.releaseStickyBalls();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key.toLowerCase()] = false;
        });
    }
    
    loadLevel() {
        const currentLevel = this.levelManager.getCurrentLevel();
        this.bricks = this.levelManager.createBricksForLevel(currentLevel);
        this.gnomes = [];
        this.projectiles = [];
        this.powerUps = [];
        this.particleSystem.clear();
        
        // Reset ball position
        this.balls = [new Ball(this.width / 2, this.height - 70)];
        this.paddle.position.x = this.width / 2 - this.paddle.width / 2;
        
        this.updateUI();
    }
    
    start() {
        this.gameState = 'playing';
        this.gameLoop();
    }
    
    pause() {
        this.gameState = this.gameState === 'paused' ? 'playing' : 'paused';
    }
    
    restart() {
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        this.levelManager.resetToLevel(1);
        this.powerUpManager.clearAllPowerUps();
        this.loadLevel();
        this.gameState = 'playing';
        this.updateUI();
    }
    
    gameLoop(currentTime) {
        if (this.gameState !== 'playing') {
            if (this.gameState !== 'gameOver' && this.gameState !== 'levelComplete') {
                requestAnimationFrame(this.gameLoop);
            }
            return;
        }
        
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        this.update(deltaTime);
        this.draw();
        
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        // Update FPS counter
        this.frameCount++;
        this.fpsTimer += deltaTime;
        if (this.fpsTimer >= 1.0) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.fpsTimer = 0;
        }

        // Handle input
        this.handleInput();
        
        // Update game objects
        this.paddle.update(deltaTime, this.width);
        
        // Update balls
        this.balls.forEach(ball => {
            ball.update(deltaTime, this.width, this.height, this.paddle);
        });
        
        // Remove inactive balls
        this.balls = this.balls.filter(ball => ball.active);
        
        // Check if all balls are lost
        if (this.balls.length === 0) {
            this.loseLife();
        }
        
        // Update gnomes
        this.gnomes.forEach(gnome => {
            gnome.update(deltaTime, this.width, this.height);
            
            // Check if gnome should throw projectile
            if (gnome.shouldThrow()) {
                this.createProjectile(gnome);
            }
        });
        
        // Remove inactive gnomes
        this.gnomes = this.gnomes.filter(gnome => gnome.active);
        
        // Update projectiles
        this.projectiles.forEach(projectile => {
            projectile.update(deltaTime, this.width, this.height);
        });
        
        // Remove inactive projectiles
        this.projectiles = this.projectiles.filter(projectile => projectile.active);
        
        // Update power-ups
        this.powerUps.forEach(powerUp => {
            powerUp.update(deltaTime, this.height);
        });
        
        // Remove inactive power-ups
        this.powerUps = this.powerUps.filter(powerUp => powerUp.active);
        
        // Update managers
        this.powerUpManager.update(deltaTime);
        this.particleSystem.update(deltaTime);
        
        // Check collisions
        this.checkCollisions();
        
        // Check win condition
        if (this.bricks.filter(brick => brick.active).length === 0) {
            this.levelComplete();
        }
    }
    
    handleInput() {
        if (this.keys['a'] || this.keys['arrowleft']) {
            this.paddle.moveLeft();
        } else if (this.keys['d'] || this.keys['arrowright']) {
            this.paddle.moveRight();
        } else {
            this.paddle.stop();
        }
    }
    
    checkCollisions() {
        // Ball-paddle collisions
        this.balls.forEach(ball => {
            if (ball.bounds.intersects(this.paddle.bounds) && ball.velocity.y > 0) {
                ball.bounceOffPaddle(this.paddle);
            }
        });
        
        // Ball-brick collisions
        this.balls.forEach(ball => {
            this.bricks.forEach(brick => {
                if (brick.active && ball.bounds.intersects(brick.bounds)) {
                    this.handleBallBrickCollision(ball, brick);
                }
            });
        });
        
        // Ball-gnome collisions
        this.balls.forEach(ball => {
            this.gnomes.forEach(gnome => {
                if (gnome.active && !gnome.isBeingBounced && ball.bounds.intersects(gnome.bounds)) {
                    this.handleBallGnomeCollision(ball, gnome);
                }
            });
        });
        
        // Projectile-paddle collisions
        this.projectiles.forEach(projectile => {
            if (projectile.bounds.intersects(this.paddle.bounds)) {
                projectile.active = false;
                this.shakeScreen();
            }
        });
        
        // Power-up-paddle collisions
        this.powerUps.forEach(powerUp => {
            if (powerUp.bounds.intersects(this.paddle.bounds)) {
                powerUp.collect();
                this.powerUpManager.applyPowerUp(powerUp.type);
            }
        });
        
        // Gnome-brick collisions (when gnome is being bounced)
        this.gnomes.forEach(gnome => {
            if (gnome.isBeingBounced) {
                this.bricks.forEach(brick => {
                    if (brick.active && gnome.bounds.intersects(brick.bounds)) {
                        this.destroyBrick(brick);
                        gnome.active = false;
                    }
                });
            }
        });
    }
    
    handleBallBrickCollision(ball, brick) {
        // Determine collision side and bounce ball
        const ballCenter = new Vector2D(ball.position.x + ball.width/2, ball.position.y + ball.height/2);
        const brickCenter = new Vector2D(brick.position.x + brick.width/2, brick.position.y + brick.height/2);
        
        const dx = ballCenter.x - brickCenter.x;
        const dy = ballCenter.y - brickCenter.y;
        
        if (Math.abs(dx) > Math.abs(dy)) {
            ball.velocity.x = -ball.velocity.x;
        } else {
            ball.velocity.y = -ball.velocity.y;
        }
        
        // Damage brick
        const destroyed = brick.hit();
        if (destroyed) {
            this.destroyBrick(brick);
        }
        
        SoundManager.playBounce();
    }
    
    handleBallGnomeCollision(ball, gnome) {
        if (gnome.canBeBounced()) {
            // Bounce gnome
            const bounceVelocity = new Vector2D(
                ball.velocity.x * 0.8,
                ball.velocity.y * 0.8
            );
            gnome.startBouncing(bounceVelocity);
            
            // Reverse ball direction
            ball.velocity.x = -ball.velocity.x * 0.9;
            ball.velocity.y = -ball.velocity.y * 0.9;
            
            this.score += 50;
            this.updateUI();
        } else {
            gnome.active = false;
            this.score += 25;
            this.updateUI();
        }
        
        SoundManager.playBounce();
    }
    
    destroyBrick(brick) {
        this.score += this.getScoreForBrick(brick);
        
        // Create particles
        this.particleSystem.addParticles(
            brick.position.x + brick.width/2,
            brick.position.y + brick.height/2,
            8, brick.color
        );
        
        // Release gnome if brick has one
        if (brick.hasGnome) {
            this.createGnome(brick.position.x + brick.width/2, brick.position.y + brick.height/2);
        }
        
        // Drop power-up if brick has one
        if (brick.powerUpType) {
            this.createPowerUp(brick.position.x + brick.width/2, brick.position.y + brick.height/2, brick.powerUpType);
        }
        
        brick.active = false;
        this.updateUI();
        SoundManager.playBreak();
    }
    
    getScoreForBrick(brick) {
        const scores = {
            'normal': 10,
            'strong': 20,
            'super': 30,
            'metal': 50,
            'explosive': 25
        };
        return scores[brick.type] || 10;
    }
    
    createGnome(x, y) {
        const gnome = new Gnome(x - 8, y - 10);
        this.gnomes.push(gnome);
    }
    
    createPowerUp(x, y, type) {
        const powerUp = new PowerUp(x - 10, y - 10, type);
        this.powerUps.push(powerUp);
    }
    
    createProjectile(gnome) {
        const projectile = new Projectile(
            gnome.position.x + gnome.width/2,
            gnome.position.y + gnome.height/2,
            this.paddle.position.x + this.paddle.width/2,
            this.paddle.position.y
        );
        this.projectiles.push(projectile);
        SoundManager.playGnomeThrow();
    }
    
    loseLife() {
        this.lives--;
        this.updateUI();
        
        if (this.lives <= 0) {
            this.gameOver();
        } else {
            // Reset ball
            this.balls = [new Ball(this.width / 2, this.height - 70)];
            this.paddle.position.x = this.width / 2 - this.paddle.width / 2;
        }
    }
    
    levelComplete() {
        this.gameState = 'levelComplete';
        this.level++;
        this.levelManager.nextLevel();
        
        // Show level complete screen
        document.getElementById('levelScore').textContent = this.score;
        document.getElementById('levelCompleteScreen').classList.remove('hidden');
    }
    
    nextLevel() {
        document.getElementById('levelCompleteScreen').classList.add('hidden');
        this.loadLevel();
        this.gameState = 'playing';
        this.gameLoop();
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        
        // Show game over screen
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }
    
    shakeScreen() {
        this.canvas.classList.add('shake');
        setTimeout(() => {
            this.canvas.classList.remove('shake');
        }, 500);
    }
    
    updateUI() {
        document.getElementById('score').textContent = Utils.formatScore(this.score);
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('level').textContent = this.level;
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Draw background gradient
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#87ceeb');
        gradient.addColorStop(1, '#98fb98');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // Draw game objects
        this.bricks.forEach(brick => {
            if (brick.active) brick.draw(this.ctx);
        });
        
        this.balls.forEach(ball => {
            if (ball.active) ball.draw(this.ctx);
        });
        
        this.paddle.draw(this.ctx);
        
        this.gnomes.forEach(gnome => {
            if (gnome.active) gnome.draw(this.ctx);
        });
        
        this.projectiles.forEach(projectile => {
            if (projectile.active) projectile.draw(this.ctx);
        });
        
        this.powerUps.forEach(powerUp => {
            if (powerUp.active) powerUp.draw(this.ctx);
        });
        
        // Draw particle effects
        this.particleSystem.draw(this.ctx);
        
        // Draw active power-ups
        this.powerUpManager.drawActivePowerUps(this.ctx, this.width);

        // Draw debug info
        if (this.showDebugInfo) {
            this.drawDebugInfo();
        }

        // Draw pause overlay
        if (this.gameState === 'paused') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.fillRect(0, 0, this.width, this.height);

            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('PAUSED', this.width/2, this.height/2);
        }
    }

    drawDebugInfo() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, 10, 200, 120);

        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';

        let y = 25;
        this.ctx.fillText(`FPS: ${this.fps}`, 15, y);
        y += 15;
        this.ctx.fillText(`Balls: ${this.balls.filter(b => b.active).length}`, 15, y);
        y += 15;
        this.ctx.fillText(`Bricks: ${this.bricks.filter(b => b.active).length}`, 15, y);
        y += 15;
        this.ctx.fillText(`Gnomes: ${this.gnomes.filter(g => g.active).length}`, 15, y);
        y += 15;
        this.ctx.fillText(`Projectiles: ${this.projectiles.filter(p => p.active).length}`, 15, y);
        y += 15;
        this.ctx.fillText(`PowerUps: ${this.powerUps.filter(p => p.active).length}`, 15, y);
        y += 15;
        this.ctx.fillText(`Particles: ${this.particleSystem.particles.length}`, 15, y);
        y += 15;
        this.ctx.fillText(`Press F3 to toggle`, 15, y);
    }

    toggleDebugInfo() {
        this.showDebugInfo = !this.showDebugInfo;
    }
}
