// Game configuration for Jardinains

const GameConfig = {
    // Canvas settings
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    
    // Paddle settings
    PADDLE: {
        WIDTH: 100,
        HEIGHT: 15,
        SPEED: 400,
        COLOR: '#8b4513',
        <PERSON>R<PERSON>_WIDTH: 150,
        SMALL_WIDTH: 60
    },
    
    // Ball settings
    BALL: {
        SIZE: 12,
        SPEED: 300,
        COLOR: '#ffffff',
        FAST_MULTIPLIER: 1.5,
        SLOW_MULTIPLIER: 0.7,
        TRAIL_LENGTH: 10,
        TRAIL_LIFE: 0.3
    },
    
    // Brick settings
    BRICK: {
        WIDTH: 60,
        HEIGHT: 20,
        SPACING: 5,
        START_X: 50,
        START_Y: 50
    },
    
    // Gnome settings
    GNOME: {
        WIDTH: 16,
        HEIGHT: 20,
        THROW_INTERVAL_MIN: 2,
        THROW_INTERVAL_MAX: 4,
        MAX_BOUNCES_MIN: 3,
        MAX_BOUNCES_MAX: 8,
        SPAWN_CHANCE: 0.3
    },
    
    // Power-up settings
    POWERUP: {
        SIZE: 20,
        FALL_SPEED: 100,
        SPAWN_CHANCE: 0.15,
        DURATIONS: {
            LARGE_PADDLE: 15,
            SMALL_PADDLE: 10,
            FAST_BALL: 8,
            SLOW_BALL: 12,
            STICKY_PADDLE: 10
        }
    },
    
    // Scoring
    SCORES: {
        NORMAL_BRICK: 10,
        STRONG_BRICK: 20,
        SUPER_BRICK: 30,
        METAL_BRICK: 50,
        EXPLOSIVE_BRICK: 25,
        GNOME_BOUNCE: 50,
        GNOME_DESTROY: 25
    },
    
    // Game settings
    GAME: {
        INITIAL_LIVES: 3,
        PARTICLE_COUNT: 8,
        GRAVITY: 200,
        PROJECTILE_SPEED: 150
    },
    
    // Level progression
    LEVELS: {
        GNOME_CHANCE_INCREASE: 0.05,
        POWERUP_CHANCE_INCREASE: 0.01,
        MAX_GNOME_CHANCE: 0.6,
        MAX_POWERUP_CHANCE: 0.25,
        BRICK_DENSITY_INCREASE: 0.02,
        MAX_BRICK_DENSITY: 0.8
    },
    
    // Visual effects
    EFFECTS: {
        SHAKE_DURATION: 500,
        FLASH_DURATION: 300,
        PULSE_SPEED: 5,
        PARTICLE_LIFE_MIN: 0.5,
        PARTICLE_LIFE_MAX: 1.5,
        PARTICLE_SIZE_MIN: 2,
        PARTICLE_SIZE_MAX: 6
    },
    
    // Audio settings
    AUDIO: {
        VOLUME: 0.1,
        BOUNCE_FREQUENCY: 800,
        BREAK_FREQUENCY: 400,
        POWERUP_FREQUENCIES: [523, 659, 784], // C, E, G
        GNOME_THROW_FREQUENCY: 200,
        SOUND_DURATION: 0.1
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameConfig;
}
