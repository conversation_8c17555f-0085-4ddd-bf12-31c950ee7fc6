// Power-up system for Jardinains

class PowerUp extends GameObject {
    constructor(x, y, type) {
        super(x, y, 20, 20);
        this.type = type;
        this.velocity = new Vector2D(0, 100);
        this.color = Colors.POWERUP_COLORS[type] || '#ffffff';
        this.pulseTimer = 0;
        this.collected = false;
    }
    
    update(deltaTime, canvasHeight) {
        this.position = this.position.add(this.velocity.multiply(deltaTime));
        this.pulseTimer += deltaTime * 5;
        
        // Remove if falls off screen
        if (this.position.y > canvasHeight) {
            this.active = false;
        }
    }
    
    draw(ctx) {
        const pulse = Math.sin(this.pulseTimer) * 0.2 + 0.8;
        
        ctx.save();
        ctx.globalAlpha = pulse;
        
        // Draw power-up background
        ctx.fillStyle = this.color;
        ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
        
        // Draw border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.position.x, this.position.y, this.width, this.height);
        
        // Draw icon based on type
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        const centerX = this.position.x + this.width / 2;
        const centerY = this.position.y + this.height / 2 + 4;
        
        switch(this.type) {
            case 'MULTI_BALL':
                ctx.fillText('●●', centerX, centerY);
                break;
            case 'LARGE_PADDLE':
                ctx.fillText('━━', centerX, centerY);
                break;
            case 'SMALL_PADDLE':
                ctx.fillText('━', centerX, centerY);
                break;
            case 'FAST_BALL':
                ctx.fillText('⚡', centerX, centerY);
                break;
            case 'SLOW_BALL':
                ctx.fillText('🐌', centerX, centerY);
                break;
            case 'EXTRA_LIFE':
                ctx.fillText('♥', centerX, centerY);
                break;
            case 'STICKY_PADDLE':
                ctx.fillText('🔗', centerX, centerY);
                break;
        }
        
        ctx.restore();
    }
    
    collect() {
        this.collected = true;
        this.active = false;
        SoundManager.playPowerUp();
    }
}

class PowerUpManager {
    constructor(game) {
        this.game = game;
        this.activePowerUps = new Map();
        this.powerUpDurations = {
            'LARGE_PADDLE': 15,
            'SMALL_PADDLE': 10,
            'FAST_BALL': 8,
            'SLOW_BALL': 12,
            'STICKY_PADDLE': 10
        };
    }
    
    applyPowerUp(type) {
        switch(type) {
            case 'MULTI_BALL':
                this.applyMultiBall();
                break;
            case 'LARGE_PADDLE':
                this.applyLargePaddle();
                break;
            case 'SMALL_PADDLE':
                this.applySmallPaddle();
                break;
            case 'FAST_BALL':
                this.applyFastBall();
                break;
            case 'SLOW_BALL':
                this.applySlowBall();
                break;
            case 'EXTRA_LIFE':
                this.applyExtraLife();
                break;
            case 'STICKY_PADDLE':
                this.applyStickyPaddle();
                break;
        }
    }
    
    applyMultiBall() {
        const balls = this.game.balls.filter(ball => ball.active);
        if (balls.length > 0) {
            const originalBall = balls[0];
            
            // Create two additional balls
            for (let i = 0; i < 2; i++) {
                const newBall = new Ball(originalBall.position.x, originalBall.position.y);
                const angle = (i === 0) ? -Math.PI/6 : Math.PI/6;
                const speed = originalBall.speed;
                
                newBall.velocity = new Vector2D(
                    Math.sin(angle) * speed,
                    -Math.cos(angle) * speed
                );
                
                this.game.balls.push(newBall);
            }
        }
    }
    
    applyLargePaddle() {
        this.removePowerUp('SMALL_PADDLE'); // Remove conflicting power-up
        this.game.paddle.resize(150);
        this.activePowerUps.set('LARGE_PADDLE', this.powerUpDurations['LARGE_PADDLE']);
    }
    
    applySmallPaddle() {
        this.removePowerUp('LARGE_PADDLE'); // Remove conflicting power-up
        this.game.paddle.resize(60);
        this.activePowerUps.set('SMALL_PADDLE', this.powerUpDurations['SMALL_PADDLE']);
    }
    
    applyFastBall() {
        this.removePowerUp('SLOW_BALL'); // Remove conflicting power-up
        this.game.balls.forEach(ball => {
            if (ball.active) ball.setSpeed(ball.speed * 1.5);
        });
        this.activePowerUps.set('FAST_BALL', this.powerUpDurations['FAST_BALL']);
    }
    
    applySlowBall() {
        this.removePowerUp('FAST_BALL'); // Remove conflicting power-up
        this.game.balls.forEach(ball => {
            if (ball.active) ball.setSpeed(ball.speed * 0.7);
        });
        this.activePowerUps.set('SLOW_BALL', this.powerUpDurations['SLOW_BALL']);
    }
    
    applyExtraLife() {
        this.game.lives++;
        this.game.updateUI();
    }
    
    applyStickyPaddle() {
        this.game.paddle.makeSticky(this.powerUpDurations['STICKY_PADDLE']);
        this.activePowerUps.set('STICKY_PADDLE', this.powerUpDurations['STICKY_PADDLE']);
    }
    
    update(deltaTime) {
        // Update power-up timers
        for (const [type, timeLeft] of this.activePowerUps) {
            const newTime = timeLeft - deltaTime;
            
            if (newTime <= 0) {
                this.removePowerUp(type);
            } else {
                this.activePowerUps.set(type, newTime);
            }
        }
    }
    
    removePowerUp(type) {
        if (!this.activePowerUps.has(type)) return;
        
        switch(type) {
            case 'LARGE_PADDLE':
            case 'SMALL_PADDLE':
                this.game.paddle.resize(100); // Reset to normal size
                break;
            case 'FAST_BALL':
                this.game.balls.forEach(ball => {
                    if (ball.active) ball.setSpeed(ball.speed / 1.5);
                });
                break;
            case 'SLOW_BALL':
                this.game.balls.forEach(ball => {
                    if (ball.active) ball.setSpeed(ball.speed / 0.7);
                });
                break;
        }
        
        this.activePowerUps.delete(type);
    }
    
    clearAllPowerUps() {
        for (const type of this.activePowerUps.keys()) {
            this.removePowerUp(type);
        }
    }
    
    drawActivePowerUps(ctx, canvasWidth) {
        let yOffset = 10;
        
        for (const [type, timeLeft] of this.activePowerUps) {
            // Draw power-up indicator
            ctx.fillStyle = Colors.POWERUP_COLORS[type];
            ctx.fillRect(canvasWidth - 120, yOffset, 100, 20);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`${type}: ${Math.ceil(timeLeft)}s`, canvasWidth - 115, yOffset + 14);
            
            yOffset += 25;
        }
    }
    
    getActivePowerUpsList() {
        return Array.from(this.activePowerUps.keys());
    }
}
