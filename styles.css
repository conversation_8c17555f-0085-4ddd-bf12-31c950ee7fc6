* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameContainer {
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

#gameHeader {
    margin-bottom: 20px;
}

#gameHeader h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    color: #ffeb3b;
}

#gameStats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 10px;
}

.stat {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    border: 2px solid #4caf50;
}

.label {
    font-weight: bold;
    color: #81c784;
}

#gameCanvas {
    border: 3px solid #4caf50;
    border-radius: 10px;
    background: linear-gradient(180deg, #87ceeb 0%, #98fb98 100%);
    display: block;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#gameControls {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
}

button {
    background: linear-gradient(45deg, #4caf50, #81c784);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:hover {
    background: linear-gradient(45deg, #45a049, #66bb6a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#instructions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    text-align: left;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

#instructions h3 {
    color: #ffeb3b;
    margin-bottom: 15px;
    text-align: center;
}

#instructions ul {
    list-style: none;
    padding-left: 0;
}

#instructions li {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.screen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 15px;
    border: 3px solid #4caf50;
    z-index: 1000;
    min-width: 300px;
}

.screen h2 {
    color: #ffeb3b;
    margin-bottom: 20px;
    font-size: 2em;
}

.screen p {
    margin: 15px 0;
    font-size: 1.2em;
}

.hidden {
    display: none;
}

/* Responsive design */
@media (max-width: 900px) {
    #gameCanvas {
        width: 90vw;
        height: auto;
    }
    
    #gameStats {
        flex-direction: column;
        gap: 10px;
    }
    
    #gameControls {
        flex-direction: column;
        align-items: center;
    }
    
    #instructions {
        font-size: 14px;
    }
}

/* Animation classes */
.shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.flash {
    animation: flash 0.3s;
}

@keyframes flash {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}
