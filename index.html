<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jardinains! - Garden Gnome Breakout</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧙‍♂️</text></svg>">
</head>
<body>
    <div id="gameContainer">
        <div id="gameHeader">
            <h1>🧙‍♂️ Jardinains! 🧙‍♂️</h1>
            <div id="gameStats">
                <div class="stat">
                    <span class="label">Score:</span>
                    <span id="score">0</span>
                </div>
                <div class="stat">
                    <span class="label">Lives:</span>
                    <span id="lives">3</span>
                </div>
                <div class="stat">
                    <span class="label">Level:</span>
                    <span id="level">1</span>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div id="gameControls">
            <button id="startBtn">Start Game</button>
            <button id="pauseBtn" disabled>Pause</button>
            <button id="restartBtn">Restart</button>
        </div>
        
        <div id="instructions">
            <h3>How to Play:</h3>
            <ul>
                <li>🎮 Use <strong>A/D</strong> or <strong>Arrow Keys</strong> to move the paddle</li>
                <li>🧱 Break bricks to release garden gnomes (nains)</li>
                <li>🧙‍♂️ Gnomes will throw objects at you - avoid them!</li>
                <li>⚡ Bounce gnomes with your ball to destroy more bricks</li>
                <li>🎁 Collect power-ups for special abilities</li>
                <li>🎯 Clear all bricks to advance to the next level</li>
            </ul>
        </div>
        
        <div id="gameOverScreen" class="screen hidden">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button id="playAgainBtn">Play Again</button>
        </div>
        
        <div id="levelCompleteScreen" class="screen hidden">
            <h2>Level Complete!</h2>
            <p>Score: <span id="levelScore">0</span></p>
            <button id="nextLevelBtn">Next Level</button>
        </div>
        
        <div id="startScreen" class="screen">
            <h2>Welcome to Jardinains!</h2>
            <p>A magical garden gnome breakout adventure!</p>
            <button id="startGameBtn">Start Playing</button>
        </div>
    </div>
    
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/gameObjects.js"></script>
    <script src="js/powerups.js"></script>
    <script src="js/levels.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
