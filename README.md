# 🧙‍♂️ Jardinains! - Garden Gnome Breakout 🧙‍♂️

A fully-featured recreation of the classic Jardinains! game - a unique twist on the traditional Breakout/Arkanoid genre featuring mischievous garden gnomes!

## 🎮 Game Features

### Core Gameplay
- **Classic Breakout Mechanics**: Control a paddle to bounce a ball and destroy bricks
- **Garden Gnomes (Nains)**: The unique feature! Gnomes emerge from destroyed bricks and:
  - Throw objects (pots, rocks, flowers, mushrooms) at your paddle
  - Can be bounced around by the ball to destroy more bricks
  - Have limited bounces before disappearing
- **Multiple Ball Physics**: Realistic ball physics with angle-based bouncing
- **Particle Effects**: Visual feedback for brick destruction and impacts

### Brick Types
- **Normal Bricks**: Standard single-hit bricks in various colors
- **Strong Bricks**: Require 2 hits to destroy
- **Super Bricks**: Require 3 hits to destroy  
- **Metal Bricks**: Require 5 hits to destroy
- **Explosive Bricks**: Single hit but create special effects

### Power-ups
- **🔴 Multi-Ball**: Splits your ball into three balls
- **🟢 Large Paddle**: Increases paddle size for easier ball control
- **🟣 Small Paddle**: Decreases paddle size for extra challenge
- **⚡ Fast Ball**: Increases ball speed
- **🐌 Slow Ball**: Decreases ball speed for better control
- **❤️ Extra Life**: Adds an additional life
- **🔗 Sticky Paddle**: Balls stick to paddle until released with spacebar

### Level System
- **20+ Levels**: Hand-crafted levels with unique patterns
- **Progressive Difficulty**: More gnomes, stronger bricks, and complex layouts
- **Themed Levels**: 
  - Garden Entrance (Tutorial)
  - Flower Bed (Flower patterns)
  - Gnome Village (House patterns)
  - Mushroom Forest (Mushroom shapes)
  - Garden Castle (Castle fortress)
  - Procedurally generated levels beyond level 5

## 🎯 Controls

### Keyboard
- **A/D** or **Arrow Keys**: Move paddle left/right
- **Spacebar**: Release sticky balls
- **P**: Pause/Resume game
- **Enter**: Start game or continue after level complete
- **Escape**: Pause game
- **Ctrl+R**: Restart game

### Touch/Mobile
- **Touch and Drag**: Move paddle to touch position
- **Swipe Up**: Release sticky balls
- **Tap Canvas**: Start/Resume game

## 🏆 Scoring System

- **Normal Brick**: 10 points
- **Strong Brick**: 20 points
- **Super Brick**: 30 points
- **Metal Brick**: 50 points
- **Explosive Brick**: 25 points
- **Bouncing Gnome**: 50 points
- **Destroying Gnome**: 25 points

## 🎨 Visual Features

- **Gradient Backgrounds**: Beautiful sky-to-grass gradient
- **Particle Systems**: Explosion effects when bricks are destroyed
- **Ball Trails**: Visual trail following the ball
- **Gnome Animations**: Detailed gnome sprites with hats, beards, and throwing animations
- **Power-up Indicators**: Visual icons showing active power-ups
- **Screen Shake**: Impact feedback when hit by projectiles
- **Responsive Design**: Adapts to different screen sizes

## 🔧 Technical Features

- **Pure JavaScript**: No external dependencies
- **Canvas Rendering**: Smooth 60fps gameplay
- **Collision Detection**: Precise rectangle and circle collision systems
- **Local Storage**: Saves high scores and statistics
- **Mobile Responsive**: Touch controls and responsive layout
- **Debug Console**: Developer commands for testing

## 📱 Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Firefox Mobile
- **Requirements**: HTML5 Canvas and ES6 support

## 🚀 Getting Started

1. **Download/Clone** the game files
2. **Open** `index.html` in your web browser
3. **Click** "Start Playing" or press Enter
4. **Use** A/D keys or arrow keys to move your paddle
5. **Destroy** all bricks to advance to the next level!

## 🎮 Gameplay Tips

1. **Angle Control**: Hit the ball with different parts of the paddle to control angle
2. **Gnome Strategy**: Use bouncing gnomes to clear hard-to-reach bricks
3. **Power-up Timing**: Collect power-ups strategically for maximum effect
4. **Sticky Paddle**: Use sticky paddle power-up to line up perfect shots
5. **Multi-ball**: Try to keep all balls in play for maximum brick destruction

## 🐛 Debug Commands

Open browser console and use these commands:

```javascript
// Get game instance
jardinains.game()

// View statistics
jardinains.stats()

// Cheat commands
jardinains.cheat.addLife()        // Add extra life
jardinains.cheat.nextLevel()      // Skip to next level
jardinains.cheat.addScore(1000)   // Add points
jardinains.cheat.clearBricks()    // Clear all bricks
jardinains.cheat.addBalls(5)      // Add multiple balls
```

## 📊 Statistics Tracking

The game automatically tracks:
- **High Score**: Your best score across all games
- **Games Played**: Total number of games started
- **Levels Completed**: Highest level reached
- **Average Score**: Your average performance

Statistics are saved locally in your browser.

## 🎵 Audio

The game includes placeholder audio system ready for:
- Ball bounce sounds
- Brick destruction effects
- Power-up collection sounds
- Gnome throwing sounds
- Background music

## 🔄 Game States

- **Start Screen**: Welcome screen with instructions
- **Playing**: Active gameplay
- **Paused**: Game paused, can be resumed
- **Level Complete**: Celebration screen between levels
- **Game Over**: Final score and statistics display

## 🏗️ Architecture

The game is built with a modular architecture:

- **`utils.js`**: Utility classes (Vector2D, Rectangle, Circle, ParticleSystem)
- **`gameObjects.js`**: Game entities (Paddle, Ball, Brick, Gnome, Projectile)
- **`powerups.js`**: Power-up system and effects
- **`levels.js`**: Level generation and management
- **`game.js`**: Main game engine and logic
- **`main.js`**: UI handling and initialization

## 🎨 Customization

Easy to customize:
- **Colors**: Modify `Colors` object in `utils.js`
- **Levels**: Add patterns in `levels.js`
- **Power-ups**: Extend `PowerUpManager` class
- **Scoring**: Adjust values in `getScoreForBrick()`
- **Physics**: Tweak speed and bounce values

## 📄 License

This is a fan recreation of the classic Jardinains! game for educational and entertainment purposes.

---

**Enjoy the magical garden gnome adventure!** 🧙‍♂️✨
