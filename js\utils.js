// Utility functions for Jardinains game

// Vector2D class for position and velocity
class Vector2D {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }
    
    add(vector) {
        return new Vector2D(this.x + vector.x, this.y + vector.y);
    }
    
    subtract(vector) {
        return new Vector2D(this.x - vector.x, this.y - vector.y);
    }
    
    multiply(scalar) {
        return new Vector2D(this.x * scalar, this.y * scalar);
    }
    
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return new Vector2D(0, 0);
        return new Vector2D(this.x / mag, this.y / mag);
    }
    
    distance(vector) {
        return Math.sqrt(Math.pow(this.x - vector.x, 2) + Math.pow(this.y - vector.y, 2));
    }
}

// Rectangle class for collision detection
class Rectangle {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    get left() { return this.x; }
    get right() { return this.x + this.width; }
    get top() { return this.y; }
    get bottom() { return this.y + this.height; }
    get centerX() { return this.x + this.width / 2; }
    get centerY() { return this.y + this.height / 2; }
    
    intersects(rect) {
        return !(this.right < rect.left || 
                this.left > rect.right || 
                this.bottom < rect.top || 
                this.top > rect.bottom);
    }
    
    contains(point) {
        return point.x >= this.left && point.x <= this.right &&
               point.y >= this.top && point.y <= this.bottom;
    }
}

// Circle class for ball collision
class Circle {
    constructor(x, y, radius) {
        this.x = x;
        this.y = y;
        this.radius = radius;
    }
    
    intersectsRect(rect) {
        const closestX = Math.max(rect.left, Math.min(this.x, rect.right));
        const closestY = Math.max(rect.top, Math.min(this.y, rect.bottom));
        
        const distanceX = this.x - closestX;
        const distanceY = this.y - closestY;
        
        return (distanceX * distanceX + distanceY * distanceY) < (this.radius * this.radius);
    }
    
    intersectsCircle(circle) {
        const distance = Math.sqrt(
            Math.pow(this.x - circle.x, 2) + Math.pow(this.y - circle.y, 2)
        );
        return distance < (this.radius + circle.radius);
    }
}

// Utility functions
const Utils = {
    // Random number between min and max
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    // Random integer between min and max (inclusive)
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // Clamp value between min and max
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },
    
    // Linear interpolation
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },
    
    // Distance between two points
    distance(x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    },
    
    // Angle between two points
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },
    
    // Convert degrees to radians
    toRadians(degrees) {
        return degrees * Math.PI / 180;
    },
    
    // Convert radians to degrees
    toDegrees(radians) {
        return radians * 180 / Math.PI;
    },
    
    // Check if point is inside circle
    pointInCircle(px, py, cx, cy, radius) {
        return this.distance(px, py, cx, cy) <= radius;
    },
    
    // Get random color from array
    randomColor(colors) {
        return colors[this.randomInt(0, colors.length - 1)];
    },
    
    // Format score with leading zeros
    formatScore(score, digits = 6) {
        return score.toString().padStart(digits, '0');
    }
};

// Color constants
const Colors = {
    BRICK_COLORS: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'],
    GNOME_COLORS: ['#8b4513', '#a0522d', '#cd853f', '#daa520'],
    POWERUP_COLORS: {
        MULTI_BALL: '#ff6b6b',
        LARGE_PADDLE: '#4ecdc4',
        SMALL_PADDLE: '#ff9ff3',
        FAST_BALL: '#feca57',
        SLOW_BALL: '#96ceb4',
        EXTRA_LIFE: '#ff1744',
        STICKY_PADDLE: '#9c27b0'
    }
};

// Sound effects (placeholder for now)
const SoundManager = {
    sounds: {},
    
    init() {
        // Initialize audio context if needed
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    },
    
    playSound(soundName) {
        // Placeholder for sound playing
        console.log(`Playing sound: ${soundName}`);
    },
    
    playBounce() {
        this.playBounceSound();
    },

    playBreak() {
        this.playBreakSound();
    },

    playPowerUp() {
        this.playPowerUpSound();
    },

    playGnomeThrow() {
        this.playGnomeThrowSound();
    },

    // Create simple beep sounds using Web Audio API
    createBeep(frequency, duration, volume = 0.1) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'square';

        gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    },

    playBounceSound() {
        this.createBeep(800, 0.1);
    },

    playBreakSound() {
        this.createBeep(400, 0.2);
    },

    playPowerUpSound() {
        // Play ascending notes
        this.createBeep(523, 0.1); // C
        setTimeout(() => this.createBeep(659, 0.1), 100); // E
        setTimeout(() => this.createBeep(784, 0.1), 200); // G
    },

    playGnomeThrowSound() {
        this.createBeep(200, 0.15);
    }
};

// Particle system for visual effects
class Particle {
    constructor(x, y, vx, vy, color, life = 1.0) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.color = color;
        this.life = life;
        this.maxLife = life;
        this.size = Utils.random(2, 6);
    }
    
    update(deltaTime) {
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        this.vy += 200 * deltaTime; // gravity
        this.life -= deltaTime;
        return this.life > 0;
    }
    
    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
    }
    
    addParticles(x, y, count, color) {
        for (let i = 0; i < count; i++) {
            const angle = Utils.random(0, Math.PI * 2);
            const speed = Utils.random(50, 150);
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            this.particles.push(new Particle(x, y, vx, vy, color, Utils.random(0.5, 1.5)));
        }
    }
    
    update(deltaTime) {
        this.particles = this.particles.filter(particle => particle.update(deltaTime));
    }
    
    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }
    
    clear() {
        this.particles = [];
    }
}
